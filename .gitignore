# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# AI-GAL specific
data/
output/
*.log
temp/
tmp/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
/src/ai_workflow/models/anything-v5/anything-v5.safetensors
/src/ai_workflow/models/clip-vit-base-patch16/.gitattributes
/src/ai_workflow/models/clip-vit-base-patch16/config.json
/src/ai_workflow/models/clip-vit-base-patch16/flax_model.msgpack
/src/ai_workflow/models/clip-vit-base-patch16/merges.txt
/src/ai_workflow/models/clip-vit-base-patch16/preprocessor_config.json
/src/ai_workflow/models/clip-vit-base-patch16/pytorch_model.bin
/src/ai_workflow/models/clip-vit-base-patch16/README.md
/src/ai_workflow/models/clip-vit-base-patch16/special_tokens_map.json
/src/ai_workflow/models/clip-vit-base-patch16/tokenizer.json
/src/ai_workflow/models/clip-vit-base-patch16/tokenizer_config.json
/src/ai_workflow/models/clip-vit-base-patch16/vocab.json
/src/ai_workflow/models/gpt_sovits/AR/models/__init__.py
/src/ai_workflow/models/gpt_sovits/AR/models/t2s_lightning_module.py
/src/ai_workflow/models/gpt_sovits/AR/models/t2s_lightning_module_onnx.py
/src/ai_workflow/models/gpt_sovits/AR/models/t2s_model.py
/src/ai_workflow/models/gpt_sovits/AR/models/t2s_model_onnx.py
/src/ai_workflow/models/gpt_sovits/AR/models/utils.py
/src/ai_workflow/models/gpt_sovits/AR/modules/__init__.py
/src/ai_workflow/models/gpt_sovits/AR/modules/activation.py
/src/ai_workflow/models/gpt_sovits/AR/modules/activation_onnx.py
/src/ai_workflow/models/gpt_sovits/AR/modules/embedding.py
/src/ai_workflow/models/gpt_sovits/AR/modules/embedding_onnx.py
/src/ai_workflow/models/gpt_sovits/AR/modules/lr_schedulers.py
/src/ai_workflow/models/gpt_sovits/AR/modules/optim.py
/src/ai_workflow/models/gpt_sovits/AR/modules/patched_mha_with_cache.py
/src/ai_workflow/models/gpt_sovits/AR/modules/patched_mha_with_cache_onnx.py
/src/ai_workflow/models/gpt_sovits/AR/modules/scaling.py
/src/ai_workflow/models/gpt_sovits/AR/modules/transformer.py
/src/ai_workflow/models/gpt_sovits/AR/modules/transformer_onnx.py
/src/ai_workflow/models/gpt_sovits/AR/text_processing/__init__.py
/src/ai_workflow/models/gpt_sovits/AR/text_processing/phonemizer.py
/src/ai_workflow/models/gpt_sovits/AR/text_processing/symbols.py
/src/ai_workflow/models/gpt_sovits/AR/utils/__init__.py
/src/ai_workflow/models/gpt_sovits/AR/utils/initialize.py
/src/ai_workflow/models/gpt_sovits/AR/utils/io.py
/src/ai_workflow/models/gpt_sovits/AR/__init__.py
/src/ai_workflow/models/gpt_sovits/feature_extractor/__init__.py
/src/ai_workflow/models/gpt_sovits/feature_extractor/cnhubert.py
/src/ai_workflow/models/gpt_sovits/feature_extractor/whisper_enc.py
/src/ai_workflow/models/gpt_sovits/infer/__init__.py
/src/ai_workflow/models/gpt_sovits/infer/inference.py
/src/ai_workflow/models/gpt_sovits/infer/inference_pool.py
/src/ai_workflow/models/gpt_sovits/infer/interface.py
/src/ai_workflow/models/gpt_sovits/infer/lang_segment.py
/src/ai_workflow/models/gpt_sovits/infer/LangSegment.py
/src/ai_workflow/models/gpt_sovits/infer/librosa.py
/src/ai_workflow/models/gpt_sovits/infer/text_utils.py
/src/ai_workflow/models/gpt_sovits/module/__init__.py
/src/ai_workflow/models/gpt_sovits/module/attentions.py
/src/ai_workflow/models/gpt_sovits/module/attentions_onnx.py
/src/ai_workflow/models/gpt_sovits/module/commons.py
/src/ai_workflow/models/gpt_sovits/module/core_vq.py
/src/ai_workflow/models/gpt_sovits/module/data_utils.py
/src/ai_workflow/models/gpt_sovits/module/losses.py
/src/ai_workflow/models/gpt_sovits/module/mel_processing.py
/src/ai_workflow/models/gpt_sovits/module/models.py
/src/ai_workflow/models/gpt_sovits/module/models_onnx.py
/src/ai_workflow/models/gpt_sovits/module/modules.py
/src/ai_workflow/models/gpt_sovits/module/mrte_model.py
/src/ai_workflow/models/gpt_sovits/module/quantize.py
/src/ai_workflow/models/gpt_sovits/module/transforms.py
/src/ai_workflow/models/gpt_sovits/pretrained_models/chinese-hubert-base/config.json
/src/ai_workflow/models/gpt_sovits/pretrained_models/chinese-hubert-base/preprocessor_config.json
/src/ai_workflow/models/gpt_sovits/pretrained_models/chinese-hubert-base/pytorch_model.bin
/src/ai_workflow/models/gpt_sovits/pretrained_models/chinese-roberta-wwm-ext-large/config.json
/src/ai_workflow/models/gpt_sovits/pretrained_models/chinese-roberta-wwm-ext-large/pytorch_model.bin
/src/ai_workflow/models/gpt_sovits/pretrained_models/chinese-roberta-wwm-ext-large/tokenizer.json
/src/ai_workflow/models/gpt_sovits/pretrained_models/s1bert25hz-2kh-longer-epoch=68e-step=50232.ckpt
/src/ai_workflow/models/gpt_sovits/pretrained_models/s2G488k.pth
/src/ai_workflow/models/gpt_sovits/reference_audio/elder_voice.wav
/src/ai_workflow/models/gpt_sovits/reference_audio/gentle_female.wav
/src/ai_workflow/models/gpt_sovits/reference_audio/lively_female.wav
/src/ai_workflow/models/gpt_sovits/reference_audio/mature_male.wav
/src/ai_workflow/models/gpt_sovits/reference_audio/narrator_voice.wav
/src/ai_workflow/models/gpt_sovits/reference_audio/voice_config.json
/src/ai_workflow/models/gpt_sovits/reference_audio/young_male.wav
/src/ai_workflow/models/gpt_sovits/text/zh_normalization/__init__.py
/src/ai_workflow/models/gpt_sovits/text/zh_normalization/char_convert.py
/src/ai_workflow/models/gpt_sovits/text/zh_normalization/chronology.py
/src/ai_workflow/models/gpt_sovits/text/zh_normalization/constants.py
/src/ai_workflow/models/gpt_sovits/text/zh_normalization/num.py
/src/ai_workflow/models/gpt_sovits/text/zh_normalization/phonecode.py
/src/ai_workflow/models/gpt_sovits/text/zh_normalization/quantifier.py
/src/ai_workflow/models/gpt_sovits/text/zh_normalization/README.md
/src/ai_workflow/models/gpt_sovits/text/zh_normalization/text_normlization.py
/src/ai_workflow/models/gpt_sovits/text/__init__.py
/src/ai_workflow/models/gpt_sovits/text/chinese.py
/src/ai_workflow/models/gpt_sovits/text/cleaner.py
/src/ai_workflow/models/gpt_sovits/text/cmudict.rep
/src/ai_workflow/models/gpt_sovits/text/cmudict-fast.rep
/src/ai_workflow/models/gpt_sovits/text/engdict-hot.rep
/src/ai_workflow/models/gpt_sovits/text/engdict_cache.pickle
/src/ai_workflow/models/gpt_sovits/text/english.py
/src/ai_workflow/models/gpt_sovits/text/japanese.py
/src/ai_workflow/models/gpt_sovits/text/namedict_cache.pickle
/src/ai_workflow/models/gpt_sovits/text/opencpop-strict.txt
/src/ai_workflow/models/gpt_sovits/text/symbols.py
/src/ai_workflow/models/gpt_sovits/text/tone_sandhi.py
/src/ai_workflow/models/gpt_sovits/__init__.py
/src/ai_workflow/models/gpt_sovits/test.py
/src/ai_workflow/models/gpt_sovits/utils.py
/src/ai_workflow/models/musicgen-medium/.gitattributes
/src/ai_workflow/models/musicgen-medium/compression_state_dict.bin
/src/ai_workflow/models/musicgen-medium/config.json
/src/ai_workflow/models/musicgen-medium/generation_config.json
/src/ai_workflow/models/musicgen-medium/preprocessor_config.json
/src/ai_workflow/models/musicgen-medium/pytorch_model.bin
/src/ai_workflow/models/musicgen-medium/README.md
/src/ai_workflow/models/musicgen-medium/special_tokens_map.json
/src/ai_workflow/models/musicgen-medium/spiece.model
/src/ai_workflow/models/musicgen-medium/state_dict.bin
/src/ai_workflow/models/musicgen-medium/tokenizer.json
/src/ai_workflow/models/musicgen-medium/tokenizer_config.json
/src/ai_workflow/models/Qwen3-Embedding-0.6B/1_Pooling/config.json
/src/ai_workflow/models/Qwen3-Embedding-0.6B/.gitattributes
/src/ai_workflow/models/Qwen3-Embedding-0.6B/config.json
/src/ai_workflow/models/Qwen3-Embedding-0.6B/config_sentence_transformers.json
/src/ai_workflow/models/Qwen3-Embedding-0.6B/generation_config.json
/src/ai_workflow/models/Qwen3-Embedding-0.6B/merges.txt
/src/ai_workflow/models/Qwen3-Embedding-0.6B/model.safetensors
/src/ai_workflow/models/Qwen3-Embedding-0.6B/modules.json
/src/ai_workflow/models/Qwen3-Embedding-0.6B/README.md
/src/ai_workflow/models/Qwen3-Embedding-0.6B/tokenizer.json
/src/ai_workflow/models/Qwen3-Embedding-0.6B/tokenizer_config.json
/src/ai_workflow/models/Qwen3-Embedding-0.6B/vocab.json
/src/ai_workflow/models/__init__.py
