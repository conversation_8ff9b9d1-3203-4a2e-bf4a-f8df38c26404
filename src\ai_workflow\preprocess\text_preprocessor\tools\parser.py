"""
文件解析器类
支持PDF、DOCX、TXT文件的解析，输出统一格式的文本
"""

import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any

try:
    import pdfplumber
except ImportError:
    pdfplumber = None

try:
    import docx
except ImportError:
    docx = None


class Parser:
    """
    文件解析器类，负责根据文件类型调用不同解析器（PDF、DOCX、TXT），输出统一格式的文本
    """
    
    def __init__(self, output_dir: str = "parsed_text"):
        """
        初始化解析器
        
        Args:
            output_dir: 解析后文本的输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 检查依赖
        self._check_dependencies()
    
    def _check_dependencies(self):
        """检查必要的依赖库是否安装"""
        if pdfplumber is None:
            self.logger.warning("pdfplumber未安装，无法解析PDF文件")
        if docx is None:
            self.logger.warning("python-docx未安装，无法解析DOCX文件")
    
    def parse(self, file_path: str) -> Dict[str, Any]:
        """
        根据文件类型解析文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            包含解析结果的字典，包含text、metadata等信息
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 获取文件扩展名
        ext = file_path.suffix.lower()
        
        # 根据扩展名选择解析方法
        if ext == '.pdf':
            return self._parse_pdf(file_path)
        elif ext in ['.docx', '.doc']:
            return self._parse_docx(file_path)
        elif ext == '.txt':
            return self._parse_txt(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {ext}")
    
    def _parse_pdf(self, file_path: Path) -> Dict[str, Any]:
        """
        解析PDF文件
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            解析结果字典
        """
        if pdfplumber is None:
            raise ImportError("需要安装pdfplumber库: pip install pdfplumber")
        
        text_content = []
        metadata = {
            'file_type': 'pdf',
            'file_name': file_path.name,
            'pages': 0
        }
        
        try:
            with pdfplumber.open(file_path) as pdf:
                metadata['pages'] = len(pdf.pages)
                
                for page_num, page in enumerate(pdf.pages, 1):
                    page_text = page.extract_text()
                    if page_text:
                        text_content.append(f"[第{page_num}页]\n{page_text}\n")
                
                full_text = "\n".join(text_content)
                
                # 保存解析后的文本
                output_file = self.output_dir / f"{file_path.stem}.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(full_text)
                
                self.logger.info(f"PDF解析完成: {file_path.name}, 共{metadata['pages']}页")
                
                return {
                    'text': full_text,
                    'metadata': metadata,
                    'output_file': str(output_file)
                }
                
        except Exception as e:
            self.logger.error(f"PDF解析失败: {file_path.name}, 错误: {str(e)}")
            raise
    
    def _parse_docx(self, file_path: Path) -> Dict[str, Any]:
        """
        解析DOCX文件
        
        Args:
            file_path: DOCX文件路径
            
        Returns:
            解析结果字典
        """
        if docx is None:
            raise ImportError("需要安装python-docx库: pip install python-docx")
        
        try:
            doc = docx.Document(file_path)
            
            text_content = []
            headings = []
            metadata = {
                'file_type': 'docx',
                'file_name': file_path.name,
                'paragraphs': len(doc.paragraphs)
            }
            
            for para in doc.paragraphs:
                if para.text.strip():
                    # 检查是否为标题
                    if para.style.name.startswith('Heading'):
                        headings.append({
                            'level': para.style.name,
                            'text': para.text.strip()
                        })
                        text_content.append(f"\n## {para.text.strip()}\n")
                    else:
                        text_content.append(para.text.strip())
            
            full_text = "\n".join(text_content)
            metadata['headings'] = headings
            
            # 保存解析后的文本
            output_file = self.output_dir / f"{file_path.stem}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(full_text)
            
            self.logger.info(f"DOCX解析完成: {file_path.name}, 共{metadata['paragraphs']}段落")
            
            return {
                'text': full_text,
                'metadata': metadata,
                'output_file': str(output_file)
            }
            
        except Exception as e:
            self.logger.error(f"DOCX解析失败: {file_path.name}, 错误: {str(e)}")
            raise
    
    def _parse_txt(self, file_path: Path) -> Dict[str, Any]:
        """
        解析TXT文件
        
        Args:
            file_path: TXT文件路径
            
        Returns:
            解析结果字典
        """
        try:
            # 尝试不同编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            text = None
            used_encoding = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        text = f.read()
                    used_encoding = encoding
                    break
                except UnicodeDecodeError:
                    continue
            
            if text is None:
                raise ValueError(f"无法解码文件: {file_path}")
            
            metadata = {
                'file_type': 'txt',
                'file_name': file_path.name,
                'encoding': used_encoding,
                'lines': len(text.splitlines())
            }
            
            # 保存解析后的文本（复制到parsed_text目录）
            output_file = self.output_dir / f"{file_path.stem}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(text)
            
            self.logger.info(f"TXT解析完成: {file_path.name}, 编码: {used_encoding}")
            
            return {
                'text': text,
                'metadata': metadata,
                'output_file': str(output_file)
            }
            
        except Exception as e:
            self.logger.error(f"TXT解析失败: {file_path.name}, 错误: {str(e)}")
            raise


if __name__ == "__main__":
    # 测试代码
    parser = Parser()
    
    # 示例用法
    print("Parser类已创建，支持的文件格式: PDF, DOCX, TXT")
    print("使用方法: parser.parse('文件路径')")
